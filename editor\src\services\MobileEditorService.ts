/**
 * 移动端编辑器服务
 * 提供移动端适配的编辑器界面、触摸交互优化、移动端性能优化、离线编辑支持等功能
 */
import { EventEmitter } from '../utils/EventEmitter';

// 设备类型枚举
export enum DeviceType {
  PHONE = 'phone',
  TABLET = 'tablet',
  DESKTOP = 'desktop'
}

// 屏幕方向枚举
export enum ScreenOrientation {
  PORTRAIT = 'portrait',
  LANDSCAPE = 'landscape'
}

// 触摸手势类型枚举
export enum GestureType {
  TAP = 'tap',
  DOUBLE_TAP = 'double_tap',
  LONG_PRESS = 'long_press',
  PAN = 'pan',
  PINCH = 'pinch',
  ROTATE = 'rotate',
  SWIPE = 'swipe',
  MULTI_TOUCH = 'multi_touch'
}

// 移动端布局模式枚举
export enum MobileLayoutMode {
  COMPACT = 'compact',
  COMFORTABLE = 'comfortable',
  SPACIOUS = 'spacious',
  CUSTOM = 'custom'
}

// 性能级别枚举
export enum PerformanceLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  AUTO = 'auto'
}

// 设备信息接口
export interface DeviceInfo {
  type: DeviceType;
  screenWidth: number;
  screenHeight: number;
  pixelRatio: number;
  orientation: ScreenOrientation;
  
  // 硬件信息
  memory: number; // GB
  cores: number;
  gpu: string;
  
  // 系统信息
  platform: string;
  version: string;
  userAgent: string;
  
  // 网络信息
  connectionType: string;
  effectiveType: string;
  downlink: number;
  
  // 电池信息
  batteryLevel?: number;
  charging?: boolean;
  
  // 传感器支持
  sensors: {
    accelerometer: boolean;
    gyroscope: boolean;
    magnetometer: boolean;
    orientation: boolean;
  };
}

// 触摸事件接口
export interface TouchEvent {
  id: string;
  type: GestureType;
  timestamp: number;
  
  // 触摸点信息
  touches: TouchPoint[];
  
  // 手势数据
  gestureData: {
    startPosition: { x: number; y: number };
    currentPosition: { x: number; y: number };
    deltaPosition: { x: number; y: number };
    velocity: { x: number; y: number };
    scale?: number;
    rotation?: number;
    duration: number;
  };
  
  // 目标元素
  target: {
    elementId: string;
    elementType: string;
    bounds: { x: number; y: number; width: number; height: number };
  };
}

// 触摸点接口
export interface TouchPoint {
  id: number;
  x: number;
  y: number;
  pressure: number;
  radiusX: number;
  radiusY: number;
  rotationAngle: number;
}

// 移动端布局配置接口
export interface MobileLayoutConfig {
  mode: MobileLayoutMode;
  
  // 面板配置
  panels: {
    toolbar: {
      position: 'top' | 'bottom' | 'floating';
      size: 'small' | 'medium' | 'large';
      items: string[];
      collapsible: boolean;
    };
    
    properties: {
      position: 'left' | 'right' | 'bottom' | 'overlay';
      width: number;
      collapsible: boolean;
      autoHide: boolean;
    };
    
    hierarchy: {
      position: 'left' | 'right' | 'overlay';
      width: number;
      collapsible: boolean;
      autoHide: boolean;
    };
    
    viewport: {
      fullscreen: boolean;
      controls: {
        zoom: boolean;
        pan: boolean;
        rotate: boolean;
      };
    };
  };
  
  // 交互配置
  interaction: {
    gestureEnabled: boolean;
    multiTouchEnabled: boolean;
    hapticFeedback: boolean;
    touchSensitivity: number;
    
    // 手势映射
    gestures: {
      [key in GestureType]?: string; // 映射到的操作
    };
  };
  
  // 性能配置
  performance: {
    level: PerformanceLevel;
    maxFPS: number;
    renderQuality: number;
    enableLOD: boolean;
    enableCulling: boolean;
  };
}

// 离线数据接口
export interface OfflineData {
  id: string;
  type: 'project' | 'asset' | 'cache';
  data: any;
  size: number;
  timestamp: number;
  version: string;
  
  // 同步状态
  syncStatus: 'synced' | 'pending' | 'conflict' | 'error';
  lastSyncAt?: number;
  
  // 优先级
  priority: 'high' | 'medium' | 'low';
  
  // 过期时间
  expiresAt?: number;
}

// 性能监控数据接口
export interface MobilePerformanceData {
  timestamp: number;
  
  // 渲染性能
  fps: number;
  frameTime: number;
  renderTime: number;
  
  // 内存使用
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  
  // CPU使用
  cpuUsage: number;
  
  // 电池状态
  battery: {
    level: number;
    charging: boolean;
    chargingTime?: number;
    dischargingTime?: number;
  };
  
  // 网络状态
  network: {
    type: string;
    effectiveType: string;
    downlink: number;
    rtt: number;
  };
  
  // 温度状态
  thermal?: {
    state: 'nominal' | 'fair' | 'serious' | 'critical';
  };
}

/**
 * 移动端编辑器服务类
 */
export class MobileEditorService extends EventEmitter {
  private static instance: MobileEditorService;
  private deviceInfo: DeviceInfo | null = null;
  private layoutConfig: MobileLayoutConfig;
  private offlineData: Map<string, OfflineData> = new Map();
  private performanceData: MobilePerformanceData[] = [];
  private isOfflineMode: boolean = false;
  private _gestureRecognizer: any = null; // 手势识别器实例
  private _performanceMonitor: any = null; // 性能监控器实例

  private constructor() {
    super();
    this.layoutConfig = this.getDefaultLayoutConfig();
    this.initializeDeviceDetection();
    this.initializeGestureRecognition();
    this.initializePerformanceMonitoring();
    this.initializeOfflineSupport();
  }

  public static getInstance(): MobileEditorService {
    if (!MobileEditorService.instance) {
      MobileEditorService.instance = new MobileEditorService();
    }
    return MobileEditorService.instance;
  }

  /**
   * 初始化设备检测
   */
  private initializeDeviceDetection(): void {
    this.deviceInfo = this.detectDevice();
    
    // 监听屏幕方向变化
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        this.updateDeviceInfo();
        this.adaptToOrientation();
      }, 100);
    });

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      this.updateDeviceInfo();
      this.adaptToScreenSize();
    });
  }

  /**
   * 检测设备信息
   */
  private detectDevice(): DeviceInfo {
    const screen = window.screen;
    const navigator = window.navigator as any;
    
    // 检测设备类型
    const screenWidth = Math.max(screen.width, screen.height);
    const screenHeight = Math.min(screen.width, screen.height);
    let deviceType: DeviceType;

    // 根据屏幕尺寸确定设备类型
    console.log(`屏幕尺寸: ${screenWidth}x${screenHeight}`);
    
    if (screenWidth < 768) {
      deviceType = DeviceType.PHONE;
    } else if (screenWidth < 1024) {
      deviceType = DeviceType.TABLET;
    } else {
      deviceType = DeviceType.DESKTOP;
    }

    // 检测屏幕方向
    const orientation = screen.width > screen.height ? 
      ScreenOrientation.LANDSCAPE : ScreenOrientation.PORTRAIT;

    // 检测硬件信息
    const memory = (navigator.deviceMemory || 4);
    const cores = navigator.hardwareConcurrency || 4;

    // 检测网络信息
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    
    // 检测传感器支持
    const sensors = {
      accelerometer: 'DeviceMotionEvent' in window,
      gyroscope: 'DeviceOrientationEvent' in window,
      magnetometer: 'ondeviceorientationabsolute' in window,
      orientation: 'orientation' in screen
    };

    return {
      type: deviceType,
      screenWidth: screen.width,
      screenHeight: screen.height,
      pixelRatio: window.devicePixelRatio || 1,
      orientation,
      memory,
      cores,
      gpu: this.detectGPU(),
      platform: navigator.platform,
      version: navigator.appVersion,
      userAgent: navigator.userAgent,
      connectionType: connection?.type || 'unknown',
      effectiveType: connection?.effectiveType || 'unknown',
      downlink: connection?.downlink || 0,
      sensors
    };
  }

  /**
   * 检测GPU信息
   */
  private detectGPU(): string {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl') as WebGLRenderingContext;
      if (gl) {
        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        if (debugInfo) {
          return gl.getParameter((debugInfo as any).UNMASKED_RENDERER_WEBGL);
        }
      }
    } catch (e) {
      // Ignore errors
    }
    return 'Unknown';
  }

  /**
   * 更新设备信息
   */
  private updateDeviceInfo(): void {
    if (this.deviceInfo) {
      const newInfo = this.detectDevice();
      const oldOrientation = this.deviceInfo.orientation;
      
      this.deviceInfo = newInfo;
      
      if (oldOrientation !== newInfo.orientation) {
        this.emit('orientationChanged', {
          from: oldOrientation,
          to: newInfo.orientation
        });
      }
    }
  }

  /**
   * 初始化手势识别
   */
  private initializeGestureRecognition(): void {
    // 这里应该集成手势识别库，如 Hammer.js
    // 简化实现，直接监听触摸事件
    
    let touchStartTime = 0;
    let touchStartPos = { x: 0, y: 0 };
    let lastTouchPos = { x: 0, y: 0 };
    
    document.addEventListener('touchstart', (e) => {
      touchStartTime = Date.now();
      touchStartPos = { x: e.touches[0].clientX, y: e.touches[0].clientY };
      lastTouchPos = touchStartPos;
    });

    document.addEventListener('touchmove', (e) => {
      const currentPos = { x: e.touches[0].clientX, y: e.touches[0].clientY };
      const deltaPos = {
        x: currentPos.x - lastTouchPos.x,
        y: currentPos.y - lastTouchPos.y
      };
      
      this.handleGesture({
        type: GestureType.PAN,
        startPosition: touchStartPos,
        currentPosition: currentPos,
        deltaPosition: deltaPos
      });
      
      lastTouchPos = currentPos;
    });

    document.addEventListener('touchend', (_e) => {
      const duration = Date.now() - touchStartTime;
      const endPos = lastTouchPos;
      const distance = Math.sqrt(
        Math.pow(endPos.x - touchStartPos.x, 2) + 
        Math.pow(endPos.y - touchStartPos.y, 2)
      );

      if (duration < 200 && distance < 10) {
        this.handleGesture({
          type: GestureType.TAP,
          startPosition: touchStartPos,
          currentPosition: endPos,
          deltaPosition: { x: 0, y: 0 }
        });
      }
    });
  }

  /**
   * 处理手势
   */
  private handleGesture(gestureData: any): void {
    const touchEvent: TouchEvent = {
      id: `gesture_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: gestureData.type,
      timestamp: Date.now(),
      touches: [], // 简化实现
      gestureData: {
        ...gestureData,
        velocity: { x: 0, y: 0 }, // 简化实现
        duration: 0 // 简化实现
      },
      target: {
        elementId: 'unknown',
        elementType: 'unknown',
        bounds: { x: 0, y: 0, width: 0, height: 0 }
      }
    };

    this.emit('gestureDetected', touchEvent);
    
    // 根据手势类型执行相应操作
    this.executeGestureAction(touchEvent);
  }

  /**
   * 执行手势操作
   */
  private executeGestureAction(touchEvent: TouchEvent): void {
    const action = this.layoutConfig.interaction.gestures[touchEvent.type];
    if (action) {
      this.emit('gestureAction', { action, touchEvent });
    }
  }

  /**
   * 初始化性能监控
   */
  private initializePerformanceMonitoring(): void {
    // 监控FPS
    let lastTime = performance.now();
    let frameCount = 0;
    
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        this.updatePerformanceData({ fps });
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measureFPS);
    };
    
    requestAnimationFrame(measureFPS);

    // 监控内存使用
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        this.updatePerformanceData({
          memoryUsage: {
            used: memory.usedJSHeapSize,
            total: memory.totalJSHeapSize,
            percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
          }
        });
      }, 5000);
    }

    // 监控电池状态
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        const updateBattery = () => {
          this.updatePerformanceData({
            battery: {
              level: battery.level * 100,
              charging: battery.charging,
              chargingTime: battery.chargingTime,
              dischargingTime: battery.dischargingTime
            }
          });
        };
        
        updateBattery();
        battery.addEventListener('chargingchange', updateBattery);
        battery.addEventListener('levelchange', updateBattery);
      });
    }
  }

  /**
   * 更新性能数据
   */
  private updatePerformanceData(data: Partial<MobilePerformanceData>): void {
    const latest = this.performanceData[this.performanceData.length - 1];
    const defaults = {
      timestamp: Date.now(),
      fps: 60,
      frameTime: 16.67,
      renderTime: 10,
      memoryUsage: { used: 0, total: 0, percentage: 0 },
      cpuUsage: 0,
      battery: { level: 100, charging: false },
      network: { type: 'unknown', effectiveType: 'unknown', downlink: 0, rtt: 0 }
    };

    const updated = {
      ...defaults,
      ...latest,
      ...data
    };

    this.performanceData.push(updated);
    
    // 保留最近100条记录
    if (this.performanceData.length > 100) {
      this.performanceData = this.performanceData.slice(-100);
    }

    this.emit('performanceUpdated', updated);
    
    // 检查性能问题
    this.checkPerformanceIssues(updated);
  }

  /**
   * 检查性能问题
   */
  private checkPerformanceIssues(data: MobilePerformanceData): void {
    const issues: string[] = [];

    if (data.fps < 30) {
      issues.push('Low FPS detected');
    }

    if (data.memoryUsage.percentage > 80) {
      issues.push('High memory usage detected');
    }

    if (data.battery.level < 20 && !data.battery.charging) {
      issues.push('Low battery level');
    }

    if (issues.length > 0) {
      this.emit('performanceIssues', issues);
      this.optimizePerformance();
    }
  }

  /**
   * 优化性能
   */
  private optimizePerformance(): void {
    // 自动降低渲染质量
    if (this.layoutConfig.performance.level === PerformanceLevel.AUTO) {
      this.layoutConfig.performance.renderQuality = Math.max(0.5, 
        this.layoutConfig.performance.renderQuality - 0.1);
      this.layoutConfig.performance.maxFPS = Math.max(30, 
        this.layoutConfig.performance.maxFPS - 5);
      
      this.emit('performanceOptimized', this.layoutConfig.performance);
    }
  }

  /**
   * 初始化离线支持
   */
  private initializeOfflineSupport(): void {
    // 监听网络状态变化
    window.addEventListener('online', () => {
      this.isOfflineMode = false;
      this.emit('networkStatusChanged', { online: true });
      this.syncOfflineData();
    });

    window.addEventListener('offline', () => {
      this.isOfflineMode = true;
      this.emit('networkStatusChanged', { online: false });
    });

    // 初始化Service Worker用于离线缓存
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js').then((registration) => {
        this.emit('serviceWorkerRegistered', registration);
      }).catch((error) => {
        console.error('Service Worker registration failed:', error);
      });
    }
  }

  /**
   * 同步离线数据
   */
  private async syncOfflineData(): Promise<void> {
    const pendingData = Array.from(this.offlineData.values())
      .filter(data => data.syncStatus === 'pending');

    for (const data of pendingData) {
      try {
        // 这里应该实现实际的同步逻辑
        await this.uploadOfflineData(data);
        data.syncStatus = 'synced';
        data.lastSyncAt = Date.now();
      } catch (error) {
        data.syncStatus = 'error';
        console.error('Failed to sync offline data:', error);
      }
    }

    this.emit('offlineDataSynced', pendingData.length);
  }

  /**
   * 上传离线数据
   */
  private async uploadOfflineData(_data: OfflineData): Promise<void> {
    // 模拟上传过程
    return new Promise((resolve) => {
      setTimeout(resolve, 1000);
    });
  }

  /**
   * 适应屏幕方向
   */
  private adaptToOrientation(): void {
    if (!this.deviceInfo) return;

    if (this.deviceInfo.orientation === ScreenOrientation.LANDSCAPE) {
      // 横屏模式优化
      this.layoutConfig.panels.toolbar.position = 'top';
      this.layoutConfig.panels.properties.position = 'right';
      this.layoutConfig.panels.hierarchy.position = 'left';
    } else {
      // 竖屏模式优化
      this.layoutConfig.panels.toolbar.position = 'bottom';
      this.layoutConfig.panels.properties.position = 'bottom';
      this.layoutConfig.panels.hierarchy.position = 'overlay';
    }

    this.emit('layoutAdapted', this.layoutConfig);
  }

  /**
   * 适应屏幕大小
   */
  private adaptToScreenSize(): void {
    if (!this.deviceInfo) return;

    if (this.deviceInfo.type === DeviceType.PHONE) {
      this.layoutConfig.mode = MobileLayoutMode.COMPACT;
      this.layoutConfig.panels.toolbar.size = 'small';
    } else if (this.deviceInfo.type === DeviceType.TABLET) {
      this.layoutConfig.mode = MobileLayoutMode.COMFORTABLE;
      this.layoutConfig.panels.toolbar.size = 'medium';
    }

    this.emit('layoutAdapted', this.layoutConfig);
  }

  /**
   * 获取默认布局配置
   */
  private getDefaultLayoutConfig(): MobileLayoutConfig {
    return {
      mode: MobileLayoutMode.COMFORTABLE,
      panels: {
        toolbar: {
          position: 'bottom',
          size: 'medium',
          items: ['select', 'move', 'rotate', 'scale', 'delete'],
          collapsible: true
        },
        properties: {
          position: 'bottom',
          width: 300,
          collapsible: true,
          autoHide: true
        },
        hierarchy: {
          position: 'left',
          width: 250,
          collapsible: true,
          autoHide: true
        },
        viewport: {
          fullscreen: false,
          controls: {
            zoom: true,
            pan: true,
            rotate: true
          }
        }
      },
      interaction: {
        gestureEnabled: true,
        multiTouchEnabled: true,
        hapticFeedback: true,
        touchSensitivity: 1.0,
        gestures: {
          [GestureType.TAP]: 'select',
          [GestureType.DOUBLE_TAP]: 'edit',
          [GestureType.LONG_PRESS]: 'context_menu',
          [GestureType.PAN]: 'move',
          [GestureType.PINCH]: 'zoom',
          [GestureType.ROTATE]: 'rotate'
        }
      },
      performance: {
        level: PerformanceLevel.AUTO,
        maxFPS: 60,
        renderQuality: 1.0,
        enableLOD: true,
        enableCulling: true
      }
    };
  }

  // ========== 公共API方法 ==========

  /**
   * 获取设备信息
   */
  public getDeviceInfo(): DeviceInfo | null {
    return this.deviceInfo;
  }

  /**
   * 获取布局配置
   */
  public getLayoutConfig(): MobileLayoutConfig {
    return this.layoutConfig;
  }

  /**
   * 更新布局配置
   */
  public updateLayoutConfig(config: Partial<MobileLayoutConfig>): void {
    this.layoutConfig = { ...this.layoutConfig, ...config };
    this.emit('layoutConfigUpdated', this.layoutConfig);
  }

  /**
   * 获取性能数据
   */
  public getPerformanceData(): MobilePerformanceData[] {
    return this.performanceData;
  }

  /**
   * 获取最新性能数据
   */
  public getLatestPerformanceData(): MobilePerformanceData | null {
    return this.performanceData[this.performanceData.length - 1] || null;
  }

  /**
   * 保存离线数据
   */
  public saveOfflineData(data: Omit<OfflineData, 'id' | 'timestamp' | 'syncStatus'>): string {
    const id = `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const offlineData: OfflineData = {
      id,
      timestamp: Date.now(),
      syncStatus: this.isOfflineMode ? 'pending' : 'synced',
      ...data
    };

    this.offlineData.set(id, offlineData);
    this.emit('offlineDataSaved', offlineData);
    
    return id;
  }

  /**
   * 获取离线数据
   */
  public getOfflineData(id: string): OfflineData | null {
    return this.offlineData.get(id) || null;
  }

  /**
   * 获取所有离线数据
   */
  public getAllOfflineData(): OfflineData[] {
    return Array.from(this.offlineData.values());
  }

  /**
   * 删除离线数据
   */
  public deleteOfflineData(id: string): boolean {
    const deleted = this.offlineData.delete(id);
    if (deleted) {
      this.emit('offlineDataDeleted', id);
    }
    return deleted;
  }

  /**
   * 检查是否为移动设备
   */
  public isMobileDevice(): boolean {
    return this.deviceInfo?.type === DeviceType.PHONE || 
           this.deviceInfo?.type === DeviceType.TABLET;
  }

  /**
   * 检查是否为离线模式
   */
  public isOffline(): boolean {
    return this.isOfflineMode;
  }

  /**
   * 启用/禁用触觉反馈
   */
  public setHapticFeedback(enabled: boolean): void {
    this.layoutConfig.interaction.hapticFeedback = enabled;
    this.emit('hapticFeedbackChanged', enabled);
  }

  /**
   * 触发触觉反馈
   */
  public triggerHapticFeedback(type: 'light' | 'medium' | 'heavy' = 'light'): void {
    if (this.layoutConfig.interaction.hapticFeedback && 'vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30]
      };
      navigator.vibrate(patterns[type]);
    }
  }
}

export default MobileEditorService;
